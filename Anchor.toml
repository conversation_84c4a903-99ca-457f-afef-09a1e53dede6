[toolchain]
package_manager = "yarn"
anchor_version = "0.30.1"

[features]
resolution = true
skip-lint = false

[programs.localnet]
# bert_staking_sc = "G4ZJJ4vytqbUsUiGst52seNZ48t2EQMxJ6eQ4sQcBrYZ"
bert_staking_sc = "5SBAWmpeag75vcgPvnSxbibQQoKguZaa5KDdR8TBjC1N"

[registry]
url = "https://api.apr.dev"

[provider]
cluster = "localnet"
wallet = "~/.config/solana/id.json"

[scripts]
test = "yarn run ts-mocha -p ./tsconfig.json -t 1000000 tests/**/*.ts"
