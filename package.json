{"private": true, "name": "bert-staking", "license": "ISC", "scripts": {"lint:fix": "prettier */*.js \"*/**/*{.js,.ts}\" -w", "lint": "prettier */*.js \"*/**/*{.js,.ts}\" --check", "check-deps": "yarn workspaces run dependency-check . --no-dev --ignore-module '@types/*' --ignore-module 'typescript'", "build:all": "yarn workspaces run build", "clean:sdk": "rm -rf sdk/node_modules && rm -rf sdk/dist", "clean:cli": "rm -rf cli/node_modules && rm -rf cli/dist", "clean": "rm -rf node_modules && yarn clean:sdk && yarn clean:cli", "install:all": "yarn install", "cli": "yarn workspace @bert-staking/cli", "sdk": "yarn workspace @bert-staking/sdk"}, "workspaces": ["cli", "sdk"], "dependencies": {"@coral-xyz/anchor": "^0.31.0", "@kinobi-so/nodes-from-anchor": "^0.22.0", "@kinobi-so/renderers": "^0.22.0", "@kinobi-so/visitors-core": "^0.22.0", "@metaplex-foundation/umi-uploader-irys": "^0.9.1", "@metaplex-foundation/mpl-core": "^0.2.0", "@metaplex-foundation/mpl-token-metadata": "^3.4.0", "@metaplex-foundation/mpl-toolbox": "^0.9.4", "@metaplex-foundation/umi": "^0.9.2", "@metaplex-foundation/umi-bundle-defaults": "^0.9.2", "@metaplex-foundation/umi-web3js-adapters": "^0.9.2", "@solana-developers/helpers": "^2.5.6", "@solana/spl-token": "^0.4.13", "@types/commander": "^2.12.2", "anchor-bankrun": "^0.5.0", "commander": "^12.1.0", "solana-bankrun": "^0.4.0", "chalk": "^4.1.2"}, "devDependencies": {"@types/bn.js": "^5.1.0", "@types/chai": "^4.3.0", "@types/mocha": "^9.0.0", "chai": "^4.3.4", "mocha": "^9.0.3", "prettier": "^2.6.2", "ts-mocha": "^10.0.0", "typescript": "^5.7.3"}}