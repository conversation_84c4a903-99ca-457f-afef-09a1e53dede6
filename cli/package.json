{"name": "@bert-staking/cli", "version": "0.1.0", "description": "CLI tool for BERT Staking Protocol", "main": "dist/index.js", "bin": {"bert-staking": "dist/index.js"}, "scripts": {"build": "tsc", "clean": "rm -rf dist", "lint": "eslint src --ext .ts", "prepare": "yarn build"}, "dependencies": {"@bert-staking/sdk": "*", "@coral-xyz/anchor": "^0.31.0", "@solana/spl-token": "^0.4.13", "@solana/web3.js": "^1.90.1", "chalk": "^4.1.2", "commander": "^12.1.0", "dotenv": "^16.4.7", "figlet": "^1.7.0", "ora": "^5.4.1"}, "devDependencies": {"@types/figlet": "^1.5.8", "@types/node": "^20.12.0", "ts-node": "^10.9.2", "typescript": "^5.7.3"}}