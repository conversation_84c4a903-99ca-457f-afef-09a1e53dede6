{"name": "@bert-staking/sdk", "version": "0.1.0", "description": "SDK for interacting with the Bert Staking program", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "clean": "rm -rf dist", "lint": "eslint src --ext .ts", "prepare": "yarn build"}, "keywords": ["solana", "blockchain", "bert-staking", "sdk"], "author": "", "license": "MIT", "peerDependencies": {"@coral-xyz/anchor": "^0.31.0", "@solana/web3.js": "^1.73.0"}, "devDependencies": {"@types/bn.js": "^5.1.0", "@typescript-eslint/eslint-plugin": "^5.51.0", "@typescript-eslint/parser": "^5.51.0", "eslint": "^8.33.0", "typescript": "^5.7.3"}}