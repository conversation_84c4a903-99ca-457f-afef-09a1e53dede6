{"address": "5SBAWmpeag75vcgPvnSxbibQQoKguZaa5KDdR8TBjC1N", "metadata": {"name": "bert_staking_sc", "version": "0.1.0", "spec": "0.1.0", "description": "Created with <PERSON><PERSON>"}, "instructions": [{"name": "admin_activate_pool", "discriminator": [120, 32, 170, 157, 250, 216, 159, 252], "accounts": [{"name": "authority", "writable": true, "signer": true, "relations": ["config"]}, {"name": "config", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [99, 111, 110, 102, 105, 103]}, {"kind": "account", "path": "config.authority", "account": "Config"}, {"kind": "account", "path": "config.id", "account": "Config"}]}}, {"name": "pool", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [112, 111, 111, 108]}, {"kind": "account", "path": "config"}, {"kind": "account", "path": "pool.index", "account": "Pool"}]}}], "args": []}, {"name": "admin_pause_pool", "discriminator": [74, 116, 13, 230, 101, 103, 117, 68], "accounts": [{"name": "authority", "writable": true, "signer": true, "relations": ["config"]}, {"name": "config", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [99, 111, 110, 102, 105, 103]}, {"kind": "account", "path": "config.authority", "account": "Config"}, {"kind": "account", "path": "config.id", "account": "Config"}]}}, {"name": "pool", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [112, 111, 111, 108]}, {"kind": "account", "path": "config"}, {"kind": "account", "path": "pool.index", "account": "Pool"}]}}], "args": []}, {"name": "admin_set_pool_config", "discriminator": [87, 181, 217, 7, 183, 23, 15, 140], "accounts": [{"name": "authority", "writable": true, "signer": true, "relations": ["config"]}, {"name": "config", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [99, 111, 110, 102, 105, 103]}, {"kind": "account", "path": "config.authority", "account": "Config"}, {"kind": "account", "path": "config.id", "account": "Config"}]}}, {"name": "pool", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [112, 111, 111, 108]}, {"kind": "account", "path": "config"}, {"kind": "account", "path": "pool.index", "account": "Pool"}]}}], "args": [{"name": "config_params", "type": {"defined": {"name": "PoolConfigArgs"}}}]}, {"name": "admin_withdraw_tokens", "discriminator": [214, 62, 163, 202, 229, 204, 126, 142], "accounts": [{"name": "authority", "writable": true, "signer": true, "relations": ["config"]}, {"name": "config", "pda": {"seeds": [{"kind": "const", "value": [99, 111, 110, 102, 105, 103]}, {"kind": "account", "path": "config.authority", "account": "Config"}, {"kind": "account", "path": "config.id", "account": "Config"}]}}, {"name": "authority_vault", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [97, 117, 116, 104, 111, 114, 105, 116, 121, 95, 118, 97, 117, 108, 116]}, {"kind": "account", "path": "config"}, {"kind": "account", "path": "config.mint", "account": "Config"}]}, "relations": ["config"]}, {"name": "admin_withdraw_destination", "writable": true, "pda": {"seeds": [{"kind": "account", "path": "config.admin_withdraw_destination", "account": "Config"}, {"kind": "const", "value": [6, 221, 246, 225, 215, 101, 161, 147, 217, 203, 225, 70, 206, 235, 121, 172, 28, 180, 133, 237, 95, 91, 55, 145, 58, 140, 245, 133, 126, 255, 0, 169]}, {"kind": "account", "path": "config.mint", "account": "Config"}], "program": {"kind": "const", "value": [140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41, 20, 142, 13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4, 142, 123, 216, 219, 233, 248, 89]}}}, {"name": "token_program", "address": "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"}, {"name": "associated_token_program", "address": "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL"}], "args": [{"name": "amount", "type": "u64"}]}, {"name": "claim_position_nft", "discriminator": [199, 237, 5, 82, 92, 33, 149, 229], "accounts": [{"name": "owner", "writable": true, "signer": true}, {"name": "payer", "writable": true, "signer": true}, {"name": "config", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [99, 111, 110, 102, 105, 103]}, {"kind": "account", "path": "config.authority", "account": "Config"}, {"kind": "account", "path": "config.id", "account": "Config"}]}}, {"name": "pool", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [112, 111, 111, 108]}, {"kind": "account", "path": "config"}, {"kind": "account", "path": "pool.index", "account": "Pool"}]}}, {"name": "user_account", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [117, 115, 101, 114]}, {"kind": "account", "path": "owner"}, {"kind": "account", "path": "config"}]}}, {"name": "user_pool_stats", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [117, 115, 101, 114, 95, 112, 111, 111, 108, 95, 115, 116, 97, 116, 115]}, {"kind": "account", "path": "owner"}, {"kind": "account", "path": "pool"}]}}, {"name": "position", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [112, 111, 115, 105, 116, 105, 111, 110]}, {"kind": "account", "path": "owner"}, {"kind": "account", "path": "mint"}, {"kind": "account", "path": "asset"}, {"kind": "account", "path": "position.id", "account": "PositionV4"}]}}, {"name": "collection", "relations": ["config"]}, {"name": "update_authority"}, {"name": "asset", "writable": true}, {"name": "mint", "docs": ["Token mint."], "relations": ["config"]}, {"name": "token_account", "writable": true, "pda": {"seeds": [{"kind": "account", "path": "owner"}, {"kind": "const", "value": [6, 221, 246, 225, 215, 101, 161, 147, 217, 203, 225, 70, 206, 235, 121, 172, 28, 180, 133, 237, 95, 91, 55, 145, 58, 140, 245, 133, 126, 255, 0, 169]}, {"kind": "account", "path": "mint"}], "program": {"kind": "const", "value": [140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41, 20, 142, 13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4, 142, 123, 216, 219, 233, 248, 89]}}}, {"name": "vault", "writable": true, "pda": {"seeds": [{"kind": "account", "path": "config"}, {"kind": "const", "value": [6, 221, 246, 225, 215, 101, 161, 147, 217, 203, 225, 70, 206, 235, 121, 172, 28, 180, 133, 237, 95, 91, 55, 145, 58, 140, 245, 133, 126, 255, 0, 169]}, {"kind": "account", "path": "mint"}], "program": {"kind": "const", "value": [140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41, 20, 142, 13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4, 142, 123, 216, 219, 233, 248, 89]}}}, {"name": "authority_vault", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [97, 117, 116, 104, 111, 114, 105, 116, 121, 95, 118, 97, 117, 108, 116]}, {"kind": "account", "path": "config"}, {"kind": "account", "path": "mint"}]}}, {"name": "core_program", "address": "CoREENxT6tW1HoK8ypY1SxRMZTcVPm7R94rH4PZNhX7d"}, {"name": "token_program", "address": "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"}, {"name": "associated_token_program", "address": "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL"}, {"name": "system_program", "address": "11111111111111111111111111111111"}], "args": []}, {"name": "claim_position_token", "discriminator": [95, 57, 238, 130, 192, 73, 77, 124], "accounts": [{"name": "owner", "writable": true, "signer": true}, {"name": "config", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [99, 111, 110, 102, 105, 103]}, {"kind": "account", "path": "config.authority", "account": "Config"}, {"kind": "account", "path": "config.id", "account": "Config"}]}}, {"name": "pool", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [112, 111, 111, 108]}, {"kind": "account", "path": "config"}, {"kind": "account", "path": "pool.index", "account": "Pool"}]}}, {"name": "user_account", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [117, 115, 101, 114]}, {"kind": "account", "path": "owner"}, {"kind": "account", "path": "config"}]}}, {"name": "user_pool_stats", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [117, 115, 101, 114, 95, 112, 111, 111, 108, 95, 115, 116, 97, 116, 115]}, {"kind": "account", "path": "owner"}, {"kind": "account", "path": "pool"}]}}, {"name": "position", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [112, 111, 115, 105, 116, 105, 111, 110]}, {"kind": "account", "path": "owner"}, {"kind": "account", "path": "mint"}, {"kind": "account", "path": "position.id", "account": "PositionV4"}]}}, {"name": "collection", "relations": ["config"]}, {"name": "mint", "docs": ["Token mint."], "relations": ["config"]}, {"name": "token_account", "writable": true, "pda": {"seeds": [{"kind": "account", "path": "owner"}, {"kind": "const", "value": [6, 221, 246, 225, 215, 101, 161, 147, 217, 203, 225, 70, 206, 235, 121, 172, 28, 180, 133, 237, 95, 91, 55, 145, 58, 140, 245, 133, 126, 255, 0, 169]}, {"kind": "account", "path": "mint"}], "program": {"kind": "const", "value": [140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41, 20, 142, 13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4, 142, 123, 216, 219, 233, 248, 89]}}}, {"name": "vault", "writable": true, "pda": {"seeds": [{"kind": "account", "path": "config"}, {"kind": "const", "value": [6, 221, 246, 225, 215, 101, 161, 147, 217, 203, 225, 70, 206, 235, 121, 172, 28, 180, 133, 237, 95, 91, 55, 145, 58, 140, 245, 133, 126, 255, 0, 169]}, {"kind": "account", "path": "mint"}], "program": {"kind": "const", "value": [140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41, 20, 142, 13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4, 142, 123, 216, 219, 233, 248, 89]}}}, {"name": "authority_vault", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [97, 117, 116, 104, 111, 114, 105, 116, 121, 95, 118, 97, 117, 108, 116]}, {"kind": "account", "path": "config"}, {"kind": "account", "path": "mint"}]}}, {"name": "token_program", "address": "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"}, {"name": "associated_token_program", "address": "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL"}, {"name": "system_program", "address": "11111111111111111111111111111111"}], "args": []}, {"name": "initialize", "discriminator": [175, 175, 109, 31, 13, 152, 155, 237], "accounts": [{"name": "authority", "writable": true, "signer": true}, {"name": "config", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [99, 111, 110, 102, 105, 103]}, {"kind": "account", "path": "authority"}, {"kind": "arg", "path": "id"}]}}, {"name": "mint"}, {"name": "collection"}, {"name": "vault", "writable": true, "pda": {"seeds": [{"kind": "account", "path": "config"}, {"kind": "const", "value": [6, 221, 246, 225, 215, 101, 161, 147, 217, 203, 225, 70, 206, 235, 121, 172, 28, 180, 133, 237, 95, 91, 55, 145, 58, 140, 245, 133, 126, 255, 0, 169]}, {"kind": "account", "path": "mint"}], "program": {"kind": "const", "value": [140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41, 20, 142, 13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4, 142, 123, 216, 219, 233, 248, 89]}}}, {"name": "nfts_vault"}, {"name": "admin_withdraw_destination"}, {"name": "system_program", "address": "11111111111111111111111111111111"}, {"name": "token_program"}, {"name": "associated_token_program", "address": "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL"}], "args": [{"name": "id", "type": "u64"}, {"name": "max_cap", "type": "u64"}, {"name": "nft_value_in_tokens", "type": "u64"}, {"name": "nfts_limit_per_user", "type": "u8"}]}, {"name": "initialize_auth_vault", "discriminator": [69, 42, 152, 197, 7, 14, 88, 250], "accounts": [{"name": "authority", "writable": true, "signer": true, "relations": ["config"]}, {"name": "config", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [99, 111, 110, 102, 105, 103]}, {"kind": "account", "path": "config.authority", "account": "Config"}, {"kind": "account", "path": "config.id", "account": "Config"}]}}, {"name": "mint", "relations": ["config"]}, {"name": "authority_vault", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [97, 117, 116, 104, 111, 114, 105, 116, 121, 95, 118, 97, 117, 108, 116]}, {"kind": "account", "path": "config"}, {"kind": "account", "path": "mint"}]}}, {"name": "system_program", "address": "11111111111111111111111111111111"}, {"name": "token_program"}], "args": []}, {"name": "initialize_pool", "discriminator": [95, 180, 10, 172, 84, 174, 232, 40], "accounts": [{"name": "authority", "writable": true, "signer": true, "relations": ["config"]}, {"name": "config", "writable": true}, {"name": "pool", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [112, 111, 111, 108]}, {"kind": "account", "path": "config"}, {"kind": "arg", "path": "index"}]}}, {"name": "system_program", "address": "11111111111111111111111111111111"}], "args": [{"name": "index", "type": "u32"}, {"name": "lock_period_days", "type": "u16"}, {"name": "yield_rate", "type": "u64"}, {"name": "max_nfts_cap", "type": "u32"}, {"name": "max_tokens_cap", "type": "u64"}, {"name": "max_value_cap", "type": "u64"}]}, {"name": "initiate_user", "discriminator": [32, 210, 131, 53, 204, 197, 220, 19], "accounts": [{"name": "owner", "writable": true, "signer": true}, {"name": "config", "pda": {"seeds": [{"kind": "const", "value": [99, 111, 110, 102, 105, 103]}, {"kind": "account", "path": "config.authority", "account": "Config"}, {"kind": "account", "path": "config.id", "account": "Config"}]}}, {"name": "pool", "pda": {"seeds": [{"kind": "const", "value": [112, 111, 111, 108]}, {"kind": "account", "path": "config"}, {"kind": "account", "path": "pool.index", "account": "Pool"}]}}, {"name": "user_account", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [117, 115, 101, 114]}, {"kind": "account", "path": "owner"}, {"kind": "account", "path": "config"}]}}, {"name": "mint", "relations": ["config"]}, {"name": "system_program", "address": "11111111111111111111111111111111"}], "args": []}, {"name": "stake_nft", "discriminator": [38, 27, 66, 46, 69, 65, 151, 219], "accounts": [{"name": "owner", "writable": true, "signer": true, "relations": ["asset"]}, {"name": "config", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [99, 111, 110, 102, 105, 103]}, {"kind": "account", "path": "config.authority", "account": "Config"}, {"kind": "account", "path": "config.id", "account": "Config"}]}}, {"name": "pool", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [112, 111, 111, 108]}, {"kind": "account", "path": "config"}, {"kind": "account", "path": "pool.index", "account": "Pool"}]}}, {"name": "user_account", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [117, 115, 101, 114]}, {"kind": "account", "path": "owner"}, {"kind": "account", "path": "config"}]}}, {"name": "user_pool_stats", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [117, 115, 101, 114, 95, 112, 111, 111, 108, 95, 115, 116, 97, 116, 115]}, {"kind": "account", "path": "owner"}, {"kind": "account", "path": "pool"}]}}, {"name": "position", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [112, 111, 115, 105, 116, 105, 111, 110]}, {"kind": "account", "path": "owner"}, {"kind": "account", "path": "mint"}, {"kind": "account", "path": "asset"}, {"kind": "arg", "path": "id"}]}}, {"name": "asset", "writable": true}, {"name": "nft_vault_owner"}, {"name": "collection", "writable": true, "relations": ["config"]}, {"name": "core_program", "address": "CoREENxT6tW1HoK8ypY1SxRMZTcVPm7R94rH4PZNhX7d"}, {"name": "mint", "relations": ["config"]}, {"name": "token_program", "address": "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"}, {"name": "associated_token_program", "address": "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL"}, {"name": "system_program", "address": "11111111111111111111111111111111"}, {"name": "rent", "address": "SysvarRent111111111111111111111111111111111"}], "args": [{"name": "id", "type": "u64"}]}, {"name": "stake_token", "discriminator": [191, 127, 193, 101, 37, 96, 87, 211], "accounts": [{"name": "owner", "writable": true, "signer": true}, {"name": "config", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [99, 111, 110, 102, 105, 103]}, {"kind": "account", "path": "config.authority", "account": "Config"}, {"kind": "account", "path": "config.id", "account": "Config"}]}}, {"name": "pool", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [112, 111, 111, 108]}, {"kind": "account", "path": "config"}, {"kind": "account", "path": "pool.index", "account": "Pool"}]}}, {"name": "user_account", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [117, 115, 101, 114]}, {"kind": "account", "path": "owner"}, {"kind": "account", "path": "config"}]}}, {"name": "user_pool_stats", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [117, 115, 101, 114, 95, 112, 111, 111, 108, 95, 115, 116, 97, 116, 115]}, {"kind": "account", "path": "owner"}, {"kind": "account", "path": "pool"}]}}, {"name": "position", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [112, 111, 115, 105, 116, 105, 111, 110]}, {"kind": "account", "path": "owner"}, {"kind": "account", "path": "mint"}, {"kind": "arg", "path": "id"}]}}, {"name": "mint"}, {"name": "token_account", "writable": true, "pda": {"seeds": [{"kind": "account", "path": "owner"}, {"kind": "const", "value": [6, 221, 246, 225, 215, 101, 161, 147, 217, 203, 225, 70, 206, 235, 121, 172, 28, 180, 133, 237, 95, 91, 55, 145, 58, 140, 245, 133, 126, 255, 0, 169]}, {"kind": "account", "path": "mint"}], "program": {"kind": "const", "value": [140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41, 20, 142, 13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4, 142, 123, 216, 219, 233, 248, 89]}}}, {"name": "vault", "writable": true, "pda": {"seeds": [{"kind": "account", "path": "config"}, {"kind": "const", "value": [6, 221, 246, 225, 215, 101, 161, 147, 217, 203, 225, 70, 206, 235, 121, 172, 28, 180, 133, 237, 95, 91, 55, 145, 58, 140, 245, 133, 126, 255, 0, 169]}, {"kind": "account", "path": "mint"}], "program": {"kind": "const", "value": [140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41, 20, 142, 13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4, 142, 123, 216, 219, 233, 248, 89]}}, "relations": ["config"]}, {"name": "token_program", "address": "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"}, {"name": "associated_token_program", "address": "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL"}, {"name": "system_program", "address": "11111111111111111111111111111111"}, {"name": "rent", "address": "SysvarRent111111111111111111111111111111111"}], "args": [{"name": "id", "type": "u64"}, {"name": "amount", "type": "u64"}]}], "accounts": [{"name": "BaseAssetV1", "discriminator": [0, 0, 0, 0, 0, 0, 0, 0]}, {"name": "BaseCollectionV1", "discriminator": [0, 0, 0, 0, 0, 0, 0, 0]}, {"name": "Config", "discriminator": [155, 12, 170, 224, 30, 250, 204, 130]}, {"name": "Pool", "discriminator": [241, 154, 109, 4, 17, 177, 109, 188]}, {"name": "PositionV4", "discriminator": [202, 171, 178, 46, 38, 48, 31, 36]}, {"name": "UserAccountV3", "discriminator": [184, 176, 178, 22, 50, 106, 126, 134]}, {"name": "UserPoolStatsAccount", "discriminator": [23, 136, 131, 110, 30, 91, 112, 74]}], "errors": [{"code": 6000, "name": "PositionLocked", "msg": "The staking position is still locked"}, {"code": 6001, "name": "NftLimitReached", "msg": "NFT limit per user for this pool reached"}, {"code": 6002, "name": "GlobalNftLimitReached", "msg": "Global NFT limit per user reached"}, {"code": 6003, "name": "InvalidAmount", "msg": "Invalid staking amount"}, {"code": 6004, "name": "ArithmeticOverflow", "msg": "Arithmetic overflow"}, {"code": 6005, "name": "InvalidPositionType", "msg": "Invalid position type"}, {"code": 6006, "name": "UserTokensLimitCapReached", "msg": "Tokens limit per user reached"}, {"code": 6007, "name": "PoolAlreadyPaused", "msg": "Pool paused"}, {"code": 6008, "name": "PoolAlreadyActive", "msg": "Pool is already active"}, {"code": 6009, "name": "InvalidPoolPauseState", "msg": "You can only set pool config if the pool is paused"}, {"code": 6010, "name": "InsufficientYieldFunds", "msg": "Insufficient funds in yield vault for rewards"}, {"code": 6011, "name": "AuthorityVaultAlreadyInitialized", "msg": "Authority vault already initialized"}, {"code": 6012, "name": "AuthorityVaultNotInitialized", "msg": "Authority vault not initialized"}, {"code": 6013, "name": "Unauthorized", "msg": "Unauthorized Operation"}, {"code": 6014, "name": "PoolValueLimitReached", "msg": "Pool value limit reached"}], "types": [{"name": "BaseAssetV1", "type": {"kind": "struct", "fields": [{"name": "key", "type": {"defined": {"name": "Key"}}}, {"name": "owner", "type": "pubkey"}, {"name": "update_authority", "type": {"defined": {"name": "UpdateAuthority"}}}, {"name": "name", "type": "string"}, {"name": "uri", "type": "string"}, {"name": "seq", "type": {"option": "u64"}}]}}, {"name": "BaseCollectionV1", "type": {"kind": "struct", "fields": [{"name": "key", "type": {"defined": {"name": "Key"}}}, {"name": "update_authority", "type": "pubkey"}, {"name": "name", "type": "string"}, {"name": "uri", "type": "string"}, {"name": "num_minted", "type": "u32"}, {"name": "current_size", "type": "u32"}]}}, {"name": "Config", "type": {"kind": "struct", "fields": [{"name": "id", "type": "u64"}, {"name": "authority", "type": "pubkey"}, {"name": "mint", "type": "pubkey"}, {"name": "collection", "type": "pubkey"}, {"name": "vault", "type": "pubkey"}, {"name": "authority_vault", "type": "pubkey"}, {"name": "nfts_vault", "type": "pubkey"}, {"name": "admin_withdraw_destination", "type": "pubkey"}, {"name": "pool_count", "type": "u32"}, {"name": "max_cap", "type": "u64"}, {"name": "nft_value_in_tokens", "type": "u64"}, {"name": "nfts_limit_per_user", "type": "u8"}, {"name": "total_staked_amount", "type": "u64"}, {"name": "total_nfts_staked", "type": "u64"}, {"name": "bump", "type": "u8"}, {"name": "authority_vault_bump", "type": "u8"}, {"name": "_padding", "type": {"array": ["u8", 96]}}]}}, {"name": "Key", "type": {"kind": "enum", "variants": [{"name": "Uninitialized"}, {"name": "AssetV1"}, {"name": "HashedAssetV1"}, {"name": "PluginHeaderV1"}, {"name": "PluginRegistryV1"}, {"name": "CollectionV1"}]}}, {"name": "Pool", "type": {"kind": "struct", "fields": [{"name": "config", "docs": ["Parent config reference"], "type": "pubkey"}, {"name": "index", "docs": ["Pool index for reference"], "type": "u32"}, {"name": "lock_period_days", "docs": ["The lock period in days"], "type": "u16"}, {"name": "yield_rate", "docs": ["Yield rate in basis points (e.g., 500 = 5%)"], "type": "u64"}, {"name": "max_nfts_cap", "docs": ["Maximum NFTs per user in this pool"], "type": "u32"}, {"name": "max_tokens_cap", "docs": ["Maximum tokens per user in this pool"], "type": "u64"}, {"name": "max_value_cap", "docs": ["Maximum total value in this pool (tokens + nfts * nft_value)"], "type": "u64"}, {"name": "is_paused", "docs": ["Whether the pool is paused"], "type": "bool"}, {"name": "total_nfts_staked", "docs": ["Current total NFTs staked in this pool"], "type": "u32"}, {"name": "total_tokens_staked", "docs": ["Current total tokens staked in this pool"], "type": "u64"}, {"name": "lifetime_nfts_staked", "docs": ["All-time NFTs staked in this pool"], "type": "u32"}, {"name": "lifetime_tokens_staked", "docs": ["All-time tokens staked in this pool"], "type": "u64"}, {"name": "lifetime_claimed_yield", "docs": ["All-time yield claimed from this pool"], "type": "u64"}, {"name": "bump", "docs": ["PDA bump"], "type": "u8"}, {"name": "_padding", "docs": ["Padding for future extensions"], "type": {"array": ["u8", 56]}}]}}, {"name": "PoolConfigArgs", "type": {"kind": "struct", "fields": [{"name": "lock_period_days", "type": "u16"}, {"name": "yield_rate", "type": "u64"}, {"name": "max_nfts_cap", "type": "u32"}, {"name": "max_tokens_cap", "type": "u64"}, {"name": "max_value_cap", "type": "u64"}]}}, {"name": "PositionStatus", "type": {"kind": "enum", "variants": [{"name": "Unclaimed"}, {"name": "Claimed"}]}}, {"name": "PositionType", "type": {"kind": "enum", "variants": [{"name": "NFT"}, {"name": "Token"}]}}, {"name": "PositionV4", "type": {"kind": "struct", "fields": [{"name": "owner", "docs": ["Owner of the position"], "type": "pubkey"}, {"name": "pool", "docs": ["Pool this position belongs to"], "type": "pubkey"}, {"name": "deposit_time", "docs": ["Time when deposit was made (unix timestamp)"], "type": "i64"}, {"name": "amount", "docs": ["Amount of tokens staked or value of NFT"], "type": "u64"}, {"name": "position_type", "docs": ["Type of position: NFT or Token"], "type": {"defined": {"name": "PositionType"}}}, {"name": "unlock_time", "docs": ["Time when the position can be unlocked"], "type": "i64"}, {"name": "status", "docs": ["Status of position: Unclaimed or Claimed"], "type": {"defined": {"name": "PositionStatus"}}}, {"name": "asset", "docs": ["NFT mint address (asset) - only used for NFT positions"], "type": "pubkey"}, {"name": "bump", "docs": ["PDA bump"], "type": "u8"}, {"name": "id", "docs": ["Position identifier"], "type": "u64"}, {"name": "last_claimed_at", "docs": ["Last time yield was claimed"], "type": "i64"}, {"name": "_padding", "docs": ["Padding for future extensions"], "type": {"array": ["u8", 64]}}]}}, {"name": "UpdateAuthority", "type": {"kind": "enum", "variants": [{"name": "None"}, {"name": "Address", "fields": ["pubkey"]}, {"name": "Collection", "fields": ["pubkey"]}]}}, {"name": "UserAccountV3", "type": {"kind": "struct", "fields": [{"name": "config", "docs": ["The config this user account is associated with"], "type": "pubkey"}, {"name": "total_staked_token_amount", "docs": ["Total staked token amount across all pools"], "type": "u64"}, {"name": "total_staked_nfts", "docs": ["Total staked NFTs across all pools"], "type": "u32"}, {"name": "total_staked_value", "docs": ["Total staked value across all pools (in tokens)"], "type": "u64"}, {"name": "total_claimed_yield", "docs": ["Total claimed yield across all pools"], "type": "u64"}, {"name": "bump", "docs": ["PDA bump"], "type": "u8"}, {"name": "_padding", "docs": ["Padding for future extensions"], "type": {"array": ["u8", 64]}}]}}, {"name": "UserPoolStatsAccount", "docs": ["A separate PDA for each user's stats for a specific pool"], "type": {"kind": "struct", "fields": [{"name": "user", "docs": ["The user this stats belongs to"], "type": "pubkey"}, {"name": "pool", "docs": ["The pool this stats is for"], "type": "pubkey"}, {"name": "tokens_staked", "docs": ["Total amount of tokens staked by the user in this pool"], "type": "u64"}, {"name": "nfts_staked", "docs": ["Number of NFTs staked by the user in this pool"], "type": "u32"}, {"name": "total_value", "docs": ["Total value staked by the user in this pool (in tokens)"], "type": "u64"}, {"name": "claimed_yield", "docs": ["Claimed yield from this pool"], "type": "u64"}, {"name": "bump", "docs": ["PDA bump"], "type": "u8"}, {"name": "_padding", "docs": ["Padding for future extensions"], "type": {"array": ["u8", 64]}}]}}]}